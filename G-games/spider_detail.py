# https://www.onlinegames.io/survival-island/

import requests
from bs4 import BeautifulSoup
import json
import re

def get_game_data(url):
    """
    Extract iframe URL, images, and text content from a game page on onlinegames.io
    """
    # Initialize data structure
    data = {
        "iframe_url": "",
        "images": [],
        "description": ""
    }
    
    # Make request to the webpage
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        # Parse HTML content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract text content from post__entry class
        post_entry = soup.find('div', class_='post__entry')
        if post_entry:
            data['description'] = post_entry.get_text(strip=True)
        
        # Find iframe URL
        iframe = soup.find('iframe', id='gameFrame')
        if iframe and 'src' in iframe.attrs:
            data['iframe_url'] = iframe['src']
        
        # Find all images within elements with class="post"
        post_elements = soup.find_all(class_="post")
        for post in post_elements:
            for img in post.find_all('img'):
                if 'src' in img.attrs:
                    # Extract the image source
                    src = img['src']
                    if not src.endswith('.svg') and not re.match(r'.*icon.*', src, re.IGNORECASE):
                        # Make sure URLs are absolute
                        if src.startswith('/'):
                            src = f"https://www.onlinegames.io{src}"
                        data['images'].append({
                            'url': src,
                            'alt': img.get('alt', ''),
                            'width': img.get('width', ''),
                            'height': img.get('height', '')
                        })
        
        return data
    
    except Exception as e:
        print(f"Error fetching data: {e}")
        return data


def main(url,csv_file):
    data = get_game_data(url)
    
    # Print results
    print("\n===== GAME DATA =====\n")
    print(f"IFRAME URL: {data['iframe_url']}\n")
    
    print(f"IMAGES ({len(data['images'])}):\n")
    for i, img in enumerate(data['images'], 1):
        print(f"{i}. {img['url']}")
        if img['alt']:
            print(f"   Alt text: {img['alt']}")
        if img['width'] and img['height']:
            print(f"   Dimensions: {img['width']}x{img['height']}")
        print()
    
    print(f"DESCRIPTION:\n{data['description']}\n")
    if len(data["images"]) == 0:
        print("No images found. Skipping this game.")
        return
    image_url=data["images"][-1]["url"]
    prompt=f'很好。 继续增加新的游戏。 内容参考链接： @Web  @{url}\n嵌入iframe是：	 @{data["iframe_url"]}\n背景图片是：	 @{image_url}'
    print(prompt)
    
    # Save to CSV file
    import csv
    import os
    
    csv_file = os.path.join(os.path.dirname(__file__),csv_file)
    file_exists = os.path.isfile(csv_file)
    
    with open(csv_file, 'a', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        
        # Write header if file doesn't exist
        if not file_exists:
            writer.writerow(['URL', 'Prompt','Image URL','Iframe URL','Description'])
        
        # Write data row
        writer.writerow([url, prompt,image_url,data["iframe_url"],data["description"]])
    
    print(f"\nData saved to {csv_file}")

    print("\n===== END =====\n")
# Main execution
if __name__ == "__main__":
    url = "https://www.onlinegames.io/survival-island/"
    main(url,"game_data_test.csv")
