# 婚纱照生成工具

基于HDGSB API的智能婚纱照生成工具，可以将普通照片转换为专业婚纱照，同时保持人脸特征一致。

## 功能特点

- 🎯 **保持人脸一致**: 专门优化的提示词确保生成的婚纱照保持原有面部特征
- 🎨 **多种风格**: 支持室内、户外、教堂等多种婚纱照风格
- 🖼️ **智能图片处理**: 自动处理图片格式、尺寸和压缩
- 🎭 **遮罩支持**: 支持使用遮罩精确控制编辑区域
- 📐 **多尺寸支持**: 支持256x256、512x512、1024x1024三种尺寸

## 安装依赖

```bash
pip install requests pillow python-dotenv
```

## 配置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的HDGSB API密钥：
```
HDGSB_API_KEY=your_actual_api_key_here
```

## 使用方法

### 基础使用

```python
from llm_henapi import WeddingPhotoEditor

# 创建编辑器
editor = WeddingPhotoEditor()

# 生成婚纱照
result = editor.create_wedding_photo(
    image_path="input_photo.png",
    size="1024x1024",
    n=1,
    response_format="url"
)

print(result)
```

### 自定义风格

```python
# 中式婚纱风格
chinese_prompt = (
    "将人物转换为穿着中式红色婚纱的新娘，"
    "保持原有面部特征完全不变，"
    "添加中国传统婚礼背景，红色装饰，"
    "专业婚纱摄影风格"
)

result = editor.create_wedding_photo(
    image_path="input_photo.png",
    custom_prompt=chinese_prompt,
    size="1024x1024"
)
```

### 使用遮罩

```python
# 精确控制编辑区域
result = editor.create_wedding_photo(
    image_path="input_photo.png",
    mask_path="mask.png",  # 透明区域表示需要编辑的部分
    custom_prompt="将遮罩区域转换为婚纱",
    size="1024x1024"
)
```

## 图片要求

- **格式**: PNG格式（工具会自动转换其他格式）
- **大小**: 小于4MB
- **尺寸**: 建议方形或接近方形，工具会自动裁剪
- **质量**: 人脸清晰，光线良好的照片效果更佳

## 提示词优化建议

为了获得最佳效果，建议在提示词中包含以下要素：

1. **保持面部特征**: "保持原有面部特征不变" / "preserve original facial features"
2. **具体服装描述**: "白色婚纱" / "elegant white wedding dress"
3. **背景环境**: "教堂背景" / "church background"
4. **摄影风格**: "专业婚纱摄影" / "professional wedding photography"
5. **光线氛围**: "柔和光线" / "soft lighting"

## 示例运行

```bash
python wedding_photo_example.py
```

## API参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| image_path | str | 是 | 输入图片路径 |
| custom_prompt | str | 否 | 自定义提示词 |
| mask_path | str | 否 | 遮罩图片路径 |
| size | str | 否 | 图片尺寸 (256x256/512x512/1024x1024) |
| n | int | 否 | 生成数量 (1-10) |
| response_format | str | 否 | 返回格式 (url/b64_json) |

## 错误处理

工具包含完整的错误处理机制：

- 图片格式自动转换
- 尺寸自动调整
- API错误详细提示
- 网络异常处理

## 注意事项

1. 确保API密钥有效且有足够余额
2. 输入图片人脸要清晰可见
3. 网络连接稳定
4. 遵守API使用条款和限制
