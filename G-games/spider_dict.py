# https://www.onlinegames.io/t/action/

import requests
from bs4 import BeautifulSoup
import json
import time

def get_action_games_links(url):
    """
    Extract all href links from game cards on the action games page
    """
    # Initialize data structure for results
    games_data = {
        "games": []
    }
    
    # Make request to the webpage
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        # Parse HTML content
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all game title elements with the exact structure provided
        # <div class="c-card__title"><a href="...">Game Title</a></div>
        title_divs = soup.find_all('div', class_='c-card__title')
        print(f"Found {len(title_divs)} game title elements")
        
        # Process each title div to extract the game link
        for title_div in title_divs:
            # Look for the anchor link inside the title div
            link = title_div.find('a')
            
            if link and 'href' in link.attrs:
                href = link['href']
                # Get the title text
                title = link.get_text().strip()
                
                # Make sure this is a game link (not a tag or external link)
                if href and '/t/' not in href:
                    # Convert relative URLs to absolute
                    if not href.startswith('http'):
                        href = f"https://www.onlinegames.io{href}"
                    
                    # Add game info to our list if it's not a duplicate
                    duplicate = False
                    for game in games_data['games']:
                        if title == game['title']:
                            duplicate = True
                            break
                    
                    if not duplicate and title:
                        games_data['games'].append({
                            'title': title,
                            'url': href
                        })
        
        return games_data
    
    except Exception as e:
        print(f"Error fetching data: {e}")
        return games_data

from spider_detail import main
# Main execution
if __name__ == "__main__":
    # url = "https://www.onlinegames.io/t/action/"
    # url = "https://www.onlinegames.io/t/adventure/"
    # url = "https://www.onlinegames.io/t/racing/"
    # url = "https://www.onlinegames.io/t/shooting/"
    # url = "https://www.onlinegames.io/t/puzzle/"
    # url = "https://www.onlinegames.io/t/strategy/"
    # url = "https://www.onlinegames.io/t/sports/"
    # url = "https://www.onlinegames.io/t/simulator/"
    url = "https://www.onlinegames.io/t/strategy/"
    data = get_action_games_links(url)
    
    # Print results
    print(f"\n===== ACTION GAMES =====\n")
    print(f"Found {len(data['games'])} unique game links:\n")
    
    for i, game in enumerate(data['games'], 1):
        print(f"{i}. {game['title']}")
        print(f"   URL: {game['url']}")
        print()
        time.sleep(1)
        main(game['url'],'game_data_simulator.csv')
        print("\n===== NEXT GAME =====\n")
            
        
