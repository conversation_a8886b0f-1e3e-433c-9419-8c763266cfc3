import base64
import os
from openai import OpenAI

# Initialize OpenAI client with API key
# Option 1: Set OPENAI_API_KEY environment variable
# Option 2: Pass api_key directly to the client
api_key = os.getenv('OPENAI_API_KEY')
if not api_key:
    raise ValueError("Please set OPENAI_API_KEY environment variable or pass api_key to the client")

client = OpenAI(api_key=api_key)
image_path = "/Users/<USER>/Pictures/TX1735_10.jpg"


prompt = """
将人物转换为穿着优雅白色婚纱的新娘，保持原有面部特征和肤色不变，添加浪漫的婚礼背景，专业婚纱摄影风格
"""

result = client.images.edit(
    model="dall-e-3",
    image=[
        open(image_path, "rb"),
    ],
    prompt=prompt
)

image_base64 = result.data[0].b64_json
image_bytes = base64.b64decode(image_base64)


# Save the image to a file

with open(image_path, "wb") as f:
    f.write(image_bytes)
