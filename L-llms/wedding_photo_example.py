#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婚纱照生成示例
使用HDGSB API将普通照片转换为婚纱照，保持人脸特征一致
"""

from llm_henapi import WeddingPhotoEditor
import json

def generate_wedding_photos():
    """生成婚纱照的示例函数"""
    
    # 创建编辑器实例
    editor = WeddingPhotoEditor()
    
    # 输入图片路径（请替换为您的实际图片路径）
    input_image = "input_photo.png"
    
    # 方案1: 基础婚纱照转换
    print("正在生成基础婚纱照...")
    result1 = editor.create_wedding_photo(
        image_path=input_image,
        size="1024x1024",
        n=1,
        response_format="url"
    )
    
    print("基础婚纱照结果:")
    print(json.dumps(result1, indent=2, ensure_ascii=False))
    print("-" * 50)
    
    # 方案2: 自定义中文提示词的婚纱照
    print("正在生成自定义风格婚纱照...")
    custom_prompt = (
        "将人物转换为穿着华丽白色婚纱的优雅新娘，"
        "保持原有的面部特征、眼睛、鼻子、嘴巴和肤色完全不变，"
        "添加梦幻的婚礼教堂背景，柔和的光线，白色玫瑰花瓣飘落，"
        "专业婚纱摄影风格，高清画质，浪漫唯美"
    )
    
    result2 = editor.create_wedding_photo(
        image_path=input_image,
        custom_prompt=custom_prompt,
        size="1024x1024",
        n=2,  # 生成2张不同风格的图片
        response_format="url"
    )
    
    print("自定义婚纱照结果:")
    print(json.dumps(result2, indent=2, ensure_ascii=False))
    print("-" * 50)
    
    # 方案3: 户外婚纱照风格
    print("正在生成户外婚纱照...")
    outdoor_prompt = (
        "Transform into an elegant bride wearing a flowing white wedding gown, "
        "preserve the exact original facial features, eye shape, nose, lips and skin tone, "
        "outdoor wedding scene with beautiful garden background, golden hour lighting, "
        "professional wedding photography, romantic and dreamy atmosphere"
    )
    
    result3 = editor.create_wedding_photo(
        image_path=input_image,
        custom_prompt=outdoor_prompt,
        size="1024x1024",
        n=1,
        response_format="url"
    )
    
    print("户外婚纱照结果:")
    print(json.dumps(result3, indent=2, ensure_ascii=False))

def generate_with_mask():
    """使用遮罩的高级编辑示例"""
    
    editor = WeddingPhotoEditor()
    
    # 如果您有遮罩图片，可以指定需要编辑的区域
    input_image = "input_photo.png"
    mask_image = "mask.png"  # 遮罩图片，透明区域表示需要编辑的部分
    
    print("正在使用遮罩生成婚纱照...")
    result = editor.create_wedding_photo(
        image_path=input_image,
        mask_path=mask_image,
        custom_prompt="将遮罩区域转换为白色婚纱，保持人脸特征不变",
        size="1024x1024",
        n=1,
        response_format="url"
    )
    
    print("遮罩编辑结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    print("=== 婚纱照生成示例 ===")
    print("请确保:")
    print("1. 已设置环境变量 HDGSB_API_KEY")
    print("2. 输入图片为PNG格式且小于4MB")
    print("3. 图片最好是方形或接近方形")
    print()
    
    # 运行基础示例
    generate_wedding_photos()
    
    # 如果需要使用遮罩，取消下面的注释
    # generate_with_mask()
